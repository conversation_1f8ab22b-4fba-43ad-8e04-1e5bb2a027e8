package GUI.Components.TopNavBar;

import Utils.Constants;
import Utils.Helper;
import com.formdev.flatlaf.extras.FlatSVGIcon;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class TopNavBar {

    private JPanel rootPanel;
    private JPanel contentPanel;
    private final List<TopNavItemElement> items = new ArrayList<>();
    
    public TopNavBar(JPanel rootPanel, JPanel contentPanel) {
        this.rootPanel = rootPanel;
        // Sử dụng FlowLayout với CENTER alignment cho navigation ngang
        rootPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));
        this.contentPanel = contentPanel;

        // Ensure the rootPanel has proper size
        if (rootPanel.getPreferredSize().width < 400) {
            rootPanel.setPreferredSize(new Dimension(600, 50));
        }

        System.out.println("TopNavBar initialized with rootPanel: " + rootPanel);
        System.out.println("RootPanel preferred size: " + rootPanel.getPreferredSize());
    }
    
    public void initComponent(List<Constants.Tab> tabs){
        System.out.println("Initializing TopNavBar with " + tabs.size() + " tabs");

        tabs.forEach(tab -> {
            try {
                TopNavItem item = new TopNavItem();
                item.setKey(tab.getKey());
                item.setText(tab.getTitle());
                item.setIcon(tab.getIcon());
                item.setContentPanel(tab.getChildren() == null ? tab.getContentPanel() : null);

                System.out.println("Adding nav item: " + tab.getTitle() + " (key: " + tab.getKey() + ")");

                var navItemElement = new TopNavItemElement(new ArrayList<>(), item);
                rootPanel.add(item);

                System.out.println("Successfully added item to rootPanel: " + tab.getTitle());
            
            if (tab.getChildren() != null) {
                // Tạo dropdown menu cho các items có children
                JPopupMenu dropdownMenu = new JPopupMenu();
                dropdownMenu.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
                
                tab.getChildren().forEach(child -> {
                    JMenuItem childMenuItem = new JMenuItem();
                    childMenuItem.setText(child.getTitle());
                    childMenuItem.setIcon(child.getIcon());
                    childMenuItem.setBorder(new EmptyBorder(10, 15, 10, 15));
                    
                    childMenuItem.addActionListener(e -> {
                        // Clear selection của tất cả items
                        clearAllSelections();

                        // Set content panel
                        contentPanel.removeAll();
                        contentPanel.add(child.getContentPanel());
                        child.getContentPanel().setVisible(true);
                        contentPanel.repaint();
                        contentPanel.revalidate();

                        // Set selected state cho parent item
                        item.setSelected(true);

                        // Store current selected child info
                        item.setCurrentChildKey(child.getKey());
                    });
                    
                    dropdownMenu.add(childMenuItem);
                });
                
                // Add dropdown functionality to parent item
                item.addActionListener(e -> {
                    if (dropdownMenu.getComponentCount() > 0) {
                        dropdownMenu.show(item, 0, item.getHeight());
                    }
                });
            }
            
                items.add(navItemElement);
            } catch (Exception e) {
                System.err.println("Error adding navigation item: " + tab.getTitle());
                e.printStackTrace();
            }
        });

        System.out.println("Total navigation items added: " + items.size());

        // Force repaint and revalidate
        rootPanel.revalidate();
        rootPanel.repaint();

        initEvent();
    }
    
    public void initEvent(){
        System.out.println("Initializing events for " + items.size() + " navigation items");

        items.forEach(item -> {
            if (item.getParent().getContentPanel() != null) {
                System.out.println("Adding click event for: " + item.getParent().getText());
                item.getParent().addActionListener(e -> {
                    System.out.println("Navigation item clicked: " + item.getParent().getText());
                    // Clear selection của tất cả items
                    clearAllSelections();

                    // Set content cho item không có children
                    contentPanel.removeAll();
                    contentPanel.add(item.getParent().getContentPanel());
                    item.getParent().getContentPanel().setVisible(true);
                    contentPanel.repaint();
                    contentPanel.revalidate();
                    item.getParent().setSelected(true);
                });
            } else {
                System.out.println("Item has no content panel (dropdown): " + item.getParent().getText());
            }
        });

        // Click first item by default
        if (!items.isEmpty() && items.get(0).getParent().getContentPanel() != null) {
            System.out.println("Auto-clicking first item: " + items.get(0).getParent().getText());
            items.get(0).getParent().doClick();
        } else {
            System.out.println("No items to auto-click or first item has no content panel");
        }
    }

    private void clearAllSelections() {
        items.forEach(TopNavItemElement::clearSelection);
    }
    
    public void navigateTo(String key){
        // Clear all selections first
        clearAllSelections();

        // Find and activate the target item
        boolean found = false;
        for (TopNavItemElement item : items) {
            // Check parent item
            if (item.getParent().getKey().equals(key)) {
                if (item.getParent().getContentPanel() != null) {
                    item.getParent().doClick();
                } else {
                    item.getParent().setSelected(true);
                }
                found = true;
                break;
            }

            // Check child items (for dropdown menus)
            // This would need to be implemented if we have child navigation
        }

        if (!found) {
            System.out.println("Navigation key not found: " + key);
        }
    }
}

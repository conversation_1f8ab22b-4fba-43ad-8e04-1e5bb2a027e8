import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class TestTopNavBarReal {
    
    // Simplified Tab class for testing
    static class Tab {
        private String key;
        private String title;
        private ImageIcon icon = null;
        private JPanel contentPanel;
        private List<Tab> children = null;
        
        public Tab(String key, String title, JPanel contentPanel, List<Tab> children) {
            this.key = key;
            this.title = title;
            this.contentPanel = contentPanel;
            this.children = children;
        }
        
        // Getters
        public String getKey() { return key; }
        public String getTitle() { return title; }
        public ImageIcon getIcon() { return icon; }
        public JPanel getContentPanel() { return contentPanel; }
        public List<Tab> getChildren() { return children; }
    }
    
    // Simplified TopNavItem
    static class TopNavItem extends JButton {
        private String key;
        private JPanel contentPanel;
        private boolean selected = false;
        
        public TopNavItem() {
            initStyle();
        }
        
        private void initStyle() {
            setFont(new Font("Segoe UI", Font.PLAIN, 14));
            setBorder(BorderFactory.createEmptyBorder(12, 20, 12, 20));
            setFocusPainted(false);
            setContentAreaFilled(false);
            setOpaque(true);
            setCursor(new Cursor(Cursor.HAND_CURSOR));
            setBackground(Color.WHITE);
            setForeground(new Color(0x4a5568));
        }
        
        public void setSelected(boolean selected) {
            this.selected = selected;
            if (selected) {
                setBackground(new Color(0x3182ce));
                setForeground(Color.WHITE);
            } else {
                setBackground(Color.WHITE);
                setForeground(new Color(0x4a5568));
            }
            repaint();
        }
        
        // Getters and setters
        public String getKey() { return key; }
        public void setKey(String key) { this.key = key; }
        public JPanel getContentPanel() { return contentPanel; }
        public void setContentPanel(JPanel contentPanel) { this.contentPanel = contentPanel; }
    }
    
    // Simplified TopNavBar
    static class TopNavBar {
        private JPanel rootPanel;
        private JPanel contentPanel;
        private final List<TopNavItem> items = new ArrayList<>();
        
        public TopNavBar(JPanel rootPanel, JPanel contentPanel) {
            this.rootPanel = rootPanel;
            rootPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));
            this.contentPanel = contentPanel;
            System.out.println("TopNavBar initialized with rootPanel: " + rootPanel);
        }
        
        public void initComponent(List<Tab> tabs) {
            System.out.println("Initializing TopNavBar with " + tabs.size() + " tabs");
            
            tabs.forEach(tab -> {
                TopNavItem item = new TopNavItem();
                item.setKey(tab.getKey());
                item.setText(tab.getTitle());
                item.setContentPanel(tab.getChildren() == null ? tab.getContentPanel() : null);
                
                System.out.println("Adding nav item: " + tab.getTitle() + " (key: " + tab.getKey() + ")");
                
                rootPanel.add(item);
                
                if (tab.getChildren() != null) {
                    // Create dropdown menu
                    JPopupMenu dropdownMenu = new JPopupMenu();
                    dropdownMenu.setBorder(BorderFactory.createLineBorder(Color.LIGHT_GRAY));
                    
                    tab.getChildren().forEach(child -> {
                        JMenuItem childMenuItem = new JMenuItem(child.getTitle());
                        childMenuItem.addActionListener(e -> {
                            System.out.println("Child menu clicked: " + child.getTitle());
                            // Clear all selections
                            items.forEach(i -> i.setSelected(false));
                            
                            // Set content
                            contentPanel.removeAll();
                            contentPanel.add(child.getContentPanel());
                            child.getContentPanel().setVisible(true);
                            contentPanel.repaint();
                            contentPanel.revalidate();
                            
                            item.setSelected(true);
                        });
                        dropdownMenu.add(childMenuItem);
                    });
                    
                    // Add dropdown functionality
                    item.addActionListener(e -> {
                        System.out.println("Dropdown item clicked: " + tab.getTitle());
                        if (dropdownMenu.getComponentCount() > 0) {
                            dropdownMenu.show(item, 0, item.getHeight());
                        }
                    });
                } else {
                    // Direct navigation item
                    item.addActionListener(e -> {
                        System.out.println("Direct item clicked: " + tab.getTitle());
                        // Clear all selections
                        items.forEach(i -> i.setSelected(false));
                        
                        // Set content
                        contentPanel.removeAll();
                        contentPanel.add(item.getContentPanel());
                        item.getContentPanel().setVisible(true);
                        contentPanel.repaint();
                        contentPanel.revalidate();
                        item.setSelected(true);
                    });
                }
                
                items.add(item);
            });
            
            System.out.println("Total navigation items added: " + items.size());
            
            // Force repaint and revalidate
            rootPanel.revalidate();
            rootPanel.repaint();
            
            // Auto-click first item
            if (!items.isEmpty() && items.get(0).getContentPanel() != null) {
                System.out.println("Auto-clicking first item: " + items.get(0).getText());
                items.get(0).doClick();
            }
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("Test Real TopNavBar");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(1000, 700);
            
            // Create main panel
            JPanel mainPanel = new JPanel(new BorderLayout());
            
            // Create top navigation panel
            JPanel topNavPanel = new JPanel();
            topNavPanel.setBackground(Color.WHITE);
            topNavPanel.setPreferredSize(new Dimension(1000, 60));
            topNavPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
            topNavPanel.setLayout(new BorderLayout());
            
            // Create center panel for navigation items
            JPanel centerNavPanel = new JPanel();
            centerNavPanel.setBackground(Color.WHITE);
            
            // Create content panel
            JPanel contentPanel = new JPanel();
            contentPanel.setBackground(new Color(0xf8f9fa));
            contentPanel.setLayout(new BorderLayout());
            
            // Create user panel
            JPanel userPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            userPanel.setBackground(Color.WHITE);
            userPanel.add(new JLabel("User: Test"));
            
            // Create test tabs similar to Constants.getTabs()
            List<Tab> tabs = createTestTabs();
            
            // Initialize TopNavBar
            TopNavBar topNavBar = new TopNavBar(centerNavPanel, contentPanel);
            topNavBar.initComponent(tabs);
            
            // Add panels to top navigation
            topNavPanel.add(centerNavPanel, BorderLayout.CENTER);
            topNavPanel.add(userPanel, BorderLayout.EAST);
            
            // Add to main panel
            mainPanel.add(topNavPanel, BorderLayout.NORTH);
            mainPanel.add(contentPanel, BorderLayout.CENTER);
            
            frame.add(mainPanel);
            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
            
            System.out.println("Test frame created");
            System.out.println("Center panel component count: " + centerNavPanel.getComponentCount());
        });
    }
    
    private static List<Tab> createTestTabs() {
        List<Tab> tabs = new ArrayList<>();
        
        // Home tab (direct)
        JPanel homePanel = new JPanel();
        homePanel.add(new JLabel("Home Content", SwingConstants.CENTER));
        tabs.add(new Tab("home", "Trang chủ", homePanel, null));
        
        // Management tab (dropdown)
        List<Tab> manageChildren = new ArrayList<>();
        JPanel accountPanel = new JPanel();
        accountPanel.add(new JLabel("Account Management", SwingConstants.CENTER));
        manageChildren.add(new Tab("manage-account", "Quản lý tài khoản", accountPanel, null));
        
        JPanel productPanel = new JPanel();
        productPanel.add(new JLabel("Product Management", SwingConstants.CENTER));
        manageChildren.add(new Tab("manage-product", "Quản lý sản phẩm", productPanel, null));
        
        tabs.add(new Tab("manage", "Quản lý", null, manageChildren));
        
        // Statistics tab (dropdown)
        List<Tab> statsChildren = new ArrayList<>();
        JPanel revenuePanel = new JPanel();
        revenuePanel.add(new JLabel("Revenue Statistics", SwingConstants.CENTER));
        statsChildren.add(new Tab("stats-revenue", "Doanh thu từ máy", revenuePanel, null));
        
        tabs.add(new Tab("stats", "Thống kê", null, statsChildren));
        
        // Personal tab (direct)
        JPanel personalPanel = new JPanel();
        personalPanel.add(new JLabel("Personal Settings", SwingConstants.CENTER));
        tabs.add(new Tab("personal", "Cá nhân", personalPanel, null));
        
        return tabs;
    }
}

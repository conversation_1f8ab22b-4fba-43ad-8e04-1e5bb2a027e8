import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;

public class TestFixedLayout {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("Test Fixed Layout");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setExtendedState(JFrame.MAXIMIZED_BOTH);
            
            // Replicate MainUI layout exactly with fixes
            var contentPane = frame.getContentPane();
            contentPane.setBackground(new Color(0xedf2f7));
            contentPane.setLayout(new BorderLayout());

            // Main content panel (panel2 equivalent)
            JPanel panel2 = new JPanel();
            panel2.setBackground(new Color(0xedf2f7));
            panel2.setLayout(new BorderLayout());

            // Top navigation panel (panel3 equivalent)
            JPanel panel3 = new JPanel();
            panel3.setMinimumSize(new Dimension(32, 60));
            panel3.setMaximumSize(new Dimension(32767, 60));
            panel3.setPreferredSize(new Dimension(32, 60));
            panel3.setBackground(Color.white);
            panel3.setLayout(new BorderLayout());
            panel3.setBorder(new EmptyBorder(5, 10, 5, 10));

            // Container panel (panel4 equivalent)
            var panel4 = new JPanel();
            panel4.setLayout(new BorderLayout());
            panel4.setBorder(new EmptyBorder(20, 20, 20, 20));
            panel4.setBackground(new Color(0xedf2f7));

            // Add panels to content pane
            contentPane.add(panel3, BorderLayout.NORTH);  // Top navigation
            contentPane.add(panel4, BorderLayout.CENTER); // Main content area
            panel4.add(panel2, BorderLayout.CENTER);

            // Create user panel for top navigation
            var userPanel = new JPanel();
            userPanel.setLayout(new FlowLayout(FlowLayout.RIGHT));
            userPanel.setPreferredSize(new Dimension(400, 50));
            userPanel.setBackground(Color.white);

            var xinChaoLabel = new JLabel("Xin chào: ");
            xinChaoLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
            xinChaoLabel.setBorder(new EmptyBorder(0, 10, 0, 5));

            JLabel userLabel = new JLabel("Test User");
            userLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            userLabel.setBorder(new EmptyBorder(0, 0, 0, 15));

            JButton logoutButton = new JButton("Logout");
            logoutButton.setBackground(new Color(0x0bc5ea));
            logoutButton.setPreferredSize(new Dimension(80, 40));
            logoutButton.setBorder(new EmptyBorder(5, 5, 5, 5));
            logoutButton.setFocusPainted(false);
            logoutButton.setCursor(new Cursor(Cursor.HAND_CURSOR));

            userPanel.add(xinChaoLabel);
            userPanel.add(userLabel);
            userPanel.add(logoutButton);

            // Create center panel for navigation items (centerNavPanel equivalent) - WITH FIXES
            JPanel centerNavPanel = new JPanel();
            centerNavPanel.setBackground(Color.white);
            centerNavPanel.setPreferredSize(new Dimension(600, 50)); // Set minimum preferred size
            centerNavPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));

            // Add panels to navigation bar
            panel3.add(centerNavPanel, BorderLayout.CENTER); // Navigation items in center
            panel3.add(userPanel, BorderLayout.EAST);        // User panel on the right
            
            // Add test navigation buttons
            JButton homeBtn = createNavButton("Trang chủ");
            JButton manageBtn = createNavButton("Quản lý");
            JButton statsBtn = createNavButton("Thống kê");
            JButton personalBtn = createNavButton("Cá nhân");
            
            centerNavPanel.add(homeBtn);
            centerNavPanel.add(manageBtn);
            centerNavPanel.add(statsBtn);
            centerNavPanel.add(personalBtn);
            
            // Add content to panel2
            panel2.add(new JLabel("Main Content Area", SwingConstants.CENTER), BorderLayout.CENTER);
            
            frame.setVisible(true);
            
            System.out.println("Fixed layout test created");
            System.out.println("Center nav panel component count: " + centerNavPanel.getComponentCount());
            System.out.println("Center nav panel visible: " + centerNavPanel.isVisible());
            
            // Check sizes after frame is visible
            Timer timer = new Timer(500, e -> {
                System.out.println("After display:");
                System.out.println("Center nav panel size: " + centerNavPanel.getSize());
                System.out.println("Center nav panel preferred size: " + centerNavPanel.getPreferredSize());
                System.out.println("Panel3 (top nav) size: " + panel3.getSize());
                
                // Force repaint
                centerNavPanel.revalidate();
                centerNavPanel.repaint();
                panel3.revalidate();
                panel3.repaint();
                System.out.println("Forced repaint completed");
            });
            timer.setRepeats(false);
            timer.start();
        });
    }
    
    private static JButton createNavButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        button.setBorder(BorderFactory.createEmptyBorder(12, 20, 12, 20));
        button.setFocusPainted(false);
        button.setContentAreaFilled(false);
        button.setOpaque(true);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBackground(Color.WHITE);
        button.setForeground(new Color(0x4a5568));
        
        // Hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                button.setBackground(new Color(0xf7fafc));
                button.setForeground(new Color(0x2d3748));
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                button.setBackground(Color.WHITE);
                button.setForeground(new Color(0x4a5568));
            }
        });
        
        button.addActionListener(e -> {
            System.out.println("Button clicked: " + text);
        });
        
        return button;
    }
}

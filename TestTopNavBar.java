import javax.swing.*;
import java.awt.*;

public class TestTopNavBar {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("Test TopNavBar");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(800, 600);
            
            // Create main panel
            JPanel mainPanel = new JPanel(new BorderLayout());
            
            // Create top navigation panel
            JPanel topNavPanel = new JPanel();
            topNavPanel.setBackground(Color.WHITE);
            topNavPanel.setPreferredSize(new Dimension(800, 60));
            topNavPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
            topNavPanel.setLayout(new BorderLayout());
            
            // Create center panel for navigation items
            JPanel centerNavPanel = new JPanel();
            centerNavPanel.setBackground(Color.WHITE);
            
            // Create content panel
            JPanel contentPanel = new JPanel();
            contentPanel.setBackground(new Color(0xf8f9fa));
            contentPanel.setLayout(new BorderLayout());
            contentPanel.add(new JLabel("Content Area", SwingConstants.CENTER), BorderLayout.CENTER);
            
            // Create user panel
            JPanel userPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
            userPanel.setBackground(Color.WHITE);
            userPanel.add(new JLabel("User: Test"));
            
            // Add test navigation buttons directly
            centerNavPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));
            
            JButton homeBtn = createNavButton("Trang chủ");
            JButton manageBtn = createNavButton("Quản lý");
            JButton statsBtn = createNavButton("Thống kê");
            JButton personalBtn = createNavButton("Cá nhân");
            
            centerNavPanel.add(homeBtn);
            centerNavPanel.add(manageBtn);
            centerNavPanel.add(statsBtn);
            centerNavPanel.add(personalBtn);
            
            // Add panels to top navigation
            topNavPanel.add(centerNavPanel, BorderLayout.CENTER);
            topNavPanel.add(userPanel, BorderLayout.EAST);
            
            // Add to main panel
            mainPanel.add(topNavPanel, BorderLayout.NORTH);
            mainPanel.add(contentPanel, BorderLayout.CENTER);
            
            frame.add(mainPanel);
            frame.setLocationRelativeTo(null);
            frame.setVisible(true);
            
            System.out.println("Test frame created with navigation buttons");
            System.out.println("Center panel component count: " + centerNavPanel.getComponentCount());
        });
    }
    
    private static JButton createNavButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        button.setBorder(BorderFactory.createEmptyBorder(12, 20, 12, 20));
        button.setFocusPainted(false);
        button.setContentAreaFilled(false);
        button.setOpaque(true);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        button.setBackground(Color.WHITE);
        button.setForeground(new Color(0x4a5568));
        
        button.addActionListener(e -> {
            System.out.println("Button clicked: " + text);
        });
        
        return button;
    }
}
